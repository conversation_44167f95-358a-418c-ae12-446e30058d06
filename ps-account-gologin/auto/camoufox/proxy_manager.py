import os

# File to store the last used proxy index
PROXY_INDEX_FILE = os.path.join(os.path.dirname(__file__), "proxy_index.txt")

# List of available proxies
proxies = [
    # "74.80.254.26:4444",
    # "206.206.92.134:4444",
    # "88.209.255.209:4444",
    # "88.216.213.200:4444",
    "88.216.103.83:4444",
    # "104.171.175.151:4444",
    # "109.121.24.146:4444",
    # "109.121.25.57:4444",
    # "109.121.26.75:4444",
    # "109.233.184.105:4444",
    # "118.188.159.236:4444",
    # "216.185.53.234:4444",
    # "102.129.247.134:4444",
    # "109.121.27.133:4444",
    # "144.229.119.40:4444",
    # "198.1.232.164:4444",
    # "109.121.12.41:4444",
    # "118.188.158.182:4444",
    # "89.34.171.166:4444",
    # "109.121.13.176:4444",
    # "178.219.14.216:4444",
    # "104.171.172.49:4444",
    # "109.233.184.70:4444",
    # "118.188.159.20:4444",
    # "89.23.81.87:4444",
    # "109.121.24.17:4444",
    # "109.121.27.92:4444",
    # "102.129.191.36:4444",
    # "144.229.119.81:4444",
    # "198.1.232.78:4444",
    # "102.129.247.39:4444",
    # "206.206.96.59:4444",
    "88.216.103.5:4444",
    "198.1.232.79:4444",
    "109.121.26.204:4444",
    # "204.242.178.163:4444",
    # "109.121.24.161:4444",
    # "109.121.24.144:4444",
    "198.1.232.74:4444",
    "65.215.60.170:4444",
    "88.216.103.248:4444",
    "109.121.12.233:4444",
    "65.215.24.24:4444",
    "88.209.255.45:4444",
    # "109.121.26.124:4444",
    "202.182.73.62:4444",
    "202.182.74.27:4444",
    "65.215.31.59:4444",
    "118.188.159.14:4444",
    "198.1.232.73:4444",
    # "204.242.182.0:4444",
    "109.121.13.87:4444",
    "202.182.74.26:4444",
    "104.171.167.35:4444",
    "109.121.25.24:4444",
    "12.5.178.47:4444",
    "204.242.178.17:4444",
    "50.231.154.16:4444",
    "50.223.132.50:4444",
    "104.171.175.149:4444",
    "88.209.255.14:4444",
    "88.216.20.27:4444",
    "109.233.184.10:4444",
    # "206.206.92.2:4444",
    "65.215.31.155:4444",
    "178.219.14.83:4444",
    # "74.80.254.69:4444",
    "6***********:4444",
    "*************:4444",
    "************:4444",
    "**************:4444",
    "*************:4444",
    "*************:4444",
    "************:4444",
    "************:4444"
]


def get_last_proxy_index():
    """
    Read the last used proxy index from the file.
    If the file doesn't exist or is empty, return -1.
    """
    try:
        if not os.path.exists(PROXY_INDEX_FILE):
            return -1

        with open(PROXY_INDEX_FILE, 'r') as f:
            content = f.read().strip()
            if content:
                return int(content)
            return -1
    except Exception as e:
        print(f"Error reading proxy index: {e}")
        return -1


def save_proxy_index(index):
    """
    Save the current proxy index to the file.
    """
    try:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(PROXY_INDEX_FILE), exist_ok=True)

        with open(PROXY_INDEX_FILE, 'w') as f:
            f.write(str(index))
    except Exception as e:
        print(f"Error saving proxy index: {e}")


def get_proxy():
    """
    Get the next proxy in rotation.
    This function maintains state between calls by saving the last used index to a file.
    """
    last_index = get_last_proxy_index()
    if last_index > len(proxies) - 1:
        last_index = len(proxies) - 1

    next_index = (last_index + 1) % len(proxies)
    save_proxy_index(next_index)

    p = proxies[next_index]

    proxy = {
        "server": f"http://{p}",
        "username": "b03f165be1",
        "password": "w1q4cGzT"
    }

    return proxy