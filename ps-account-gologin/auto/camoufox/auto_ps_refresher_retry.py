"""
Script to retry refreshing PlayStation accounts from failed_try_again.txt.
"""

import os
import query_builder as qb
from typing import List, Dict, Set
from auto.camoufox.ps_browser import refresh_profile
from auto.camoufox.pull_ps_accounts import get_all_accounts
from auto.camoufox.failed_accounts_manager import add_failed_account
from auto.camoufox.account_query_manager import number_of_daily_updated_accounts
from auto.camoufox.loading_sequence import random_display_sleep


ps_public = qb.Schema('public', db_name='ps')

number_of_accounts_daily = 100
accounts_tried_untill_pause = 30

PAUSE_PER_BATCH = 1800
PAUSE_AFTER_COMPLETE = 3600

RETRY_ACCOUNTS_FILE = os.path.join(os.path.dirname(__file__), "failed_try_again.txt")
# File to store accounts that failed during retry
RETRY_FAILED_ACCOUNTS_FILE = os.path.join(os.path.dirname(__file__), "failed_retry_accounts.txt")

def get_retry_accounts() -> Set[str]:
    """
    Get the list of email addresses to retry from failed_try_again.txt,
    excluding those that have already failed during retry.

    Returns:
        A set of email addresses to retry
    """
    if not os.path.exists(RETRY_ACCOUNTS_FILE):
        print(f"Retry accounts file not found: {RETRY_ACCOUNTS_FILE}")
        return set()

    try:
        with open(RETRY_ACCOUNTS_FILE, 'r') as f:
            all_retry_emails = set(line.strip() for line in f if line.strip())

        # Get accounts that have already failed during retry
        retry_failed_emails = get_retry_failed_accounts()

        # Filter out accounts that have already failed during retry
        filtered_emails = all_retry_emails - retry_failed_emails

        if retry_failed_emails:
            print(f"Filtered out {len(retry_failed_emails)} accounts that have already failed during retry")

        return filtered_emails
    except Exception as e:
        print(f"Error reading retry accounts: {e}")
        return set()

def get_retry_failed_accounts() -> Set[str]:
    """
    Get the list of email addresses that have already failed during retry.

    Returns:
        A set of email addresses that have already failed during retry
    """
    if not os.path.exists(RETRY_FAILED_ACCOUNTS_FILE):
        return set()

    try:
        with open(RETRY_FAILED_ACCOUNTS_FILE, 'r') as f:
            emails = set(line.strip() for line in f if line.strip())
        return emails
    except Exception as e:
        print(f"Error reading retry failed accounts: {e}")
        return set()

def add_retry_failed_account(email: str) -> None:
    """
    Add an email address to the list of retry failed accounts.

    Args:
        email: The email address to add
    """
    if not email:
        return

    try:
        # Get existing retry failed accounts
        retry_failed_accounts = get_retry_failed_accounts()

        # Add the new email
        retry_failed_accounts.add(email)

        # Write back to the file
        with open(RETRY_FAILED_ACCOUNTS_FILE, 'w') as f:
            for email in sorted(retry_failed_accounts):
                f.write(f"{email}\n")

        print(f"Added {email} to retry failed accounts list")
    except Exception as e:
        print(f"Error adding retry failed account: {e}")

def remove_from_retry_list(email: str) -> None:
    if not email:
        return

    try:
        retry_accounts = get_retry_accounts()

        if email in retry_accounts:
            retry_accounts.remove(email)

            with open(RETRY_ACCOUNTS_FILE, 'w') as f:
                for email in sorted(retry_accounts):
                    f.write(f"{email}\n")

            print(f"Removed {email} from retry accounts list")
    except Exception as e:
        print(f"Error removing account from retry list: {e}")

def get_account_details(emails: Set[str]) -> List[Dict[str, str]]:
    accounts = []

    for email in emails:
        try:
            result = qb.Query(ps_public.accounts_identity).where(
                qb.c_.email == email
            ).select(qb.c_.password).fetchone_sync()

            if result and 'password' in result:
                accounts.append({
                    'email': email,
                    'password': result['password']
                })
            else:
                print(f"Warning: Could not find password for {email}")
        except Exception as e:
            print(f"Error querying account details for {email}: {e}")

    return accounts

def main():
    print("Starting PlayStation account retry refresh...")

    current_refreshed_accounts = number_of_daily_updated_accounts()
    print(f"Current refreshed accounts: {current_refreshed_accounts}")
    if current_refreshed_accounts >= number_of_accounts_daily:
        print(f"Daily limit reached. Sleeping for {PAUSE_AFTER_COMPLETE/60} minutes... Then Exiting.")
        random_display_sleep(PAUSE_AFTER_COMPLETE)
        return

    retry_emails = get_retry_accounts()
    print(f"Found {len(retry_emails)} accounts to retry")

    if not retry_emails:
        print("No accounts to retry. Exiting.")
        return

    accounts = get_account_details(retry_emails)
    print(f"Retrieved details for {len(accounts)} accounts")

    if not accounts:
        print("Could not retrieve account details. Exiting.")
        return

    batch_count = 0

    for account in accounts:
        email = account.get('email', 'unknown')
        print(f"Refreshed {current_refreshed_accounts} of {number_of_accounts_daily}: starting {email}")

        try:
            success = refresh_profile(account)

            if success:
                current_refreshed_accounts += 1
                remove_from_retry_list(email)
                print(f"✅ Account {email} refreshed successfully and removed from retry list")
            else:
                # Add to retry failed accounts (new file) instead of regular failed accounts
                add_retry_failed_account(email)
                print(f"❌ Account {email} failed to refresh and has been added to the retry failed accounts list")
        except Exception as e:
            error_message = str(e)
            if any(term in error_message.lower() for term in [
                'proxy', 'connection', 'network', 'timeout', 'socket',
                'dns', 'connect', 'unreachable', 'refused', 'reset'
            ]):
                print(f"⚠️ Account {email} encountered a proxy error - skipping without marking as failed")
            else:
                print(f"⚠️ Account {email} failed with unexpected error: {error_message}")

        batch_count += 1
        print(f"{current_refreshed_accounts}/{number_of_accounts_daily} accounts refreshed successfully so far")
        print(f"{batch_count}/{accounts_tried_untill_pause} accounts tried in current batch")

        if current_refreshed_accounts == number_of_accounts_daily:
            print("Daily limit reached. Exiting.")
            break

        if batch_count == accounts_tried_untill_pause:
            batch_count = 0
            print(f"Pausing for {PAUSE_PER_BATCH/60} minutes...")
            random_display_sleep(PAUSE_PER_BATCH)


if __name__ == "__main__":
    main()
