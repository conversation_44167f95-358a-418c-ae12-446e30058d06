"""
Module to manage the list of accounts that failed to refresh.
"""

import os
import j<PERSON>
from typing import List, Dict, Set

# File to store the failed accounts
FAILED_ACCOUNTS_FILE = os.path.join(os.path.dirname(__file__), "failed_accounts_new.txt")

def get_failed_accounts() -> Set[str]:
    """
    Get the list of email addresses that failed to refresh.
    
    Returns:
        A set of email addresses that failed to refresh
    """
    if not os.path.exists(FAILED_ACCOUNTS_FILE):
        return set()
        
    try:
        with open(FAILED_ACCOUNTS_FILE, 'r') as f:
            emails = set(line.strip() for line in f if line.strip())
        return emails
    except Exception as e:
        print(f"Error reading failed accounts: {e}")
        return set()

def add_failed_account(email: str) -> None:
    """
    Add an email address to the list of failed accounts.
    
    Args:
        email: The email address to add
    """
    if not email:
        return
        
    try:
        # Get existing failed accounts
        failed_accounts = get_failed_accounts()
        
        # Add the new email
        failed_accounts.add(email)
        
        # Write back to the file
        with open(FAILED_ACCOUNTS_FILE, 'w') as f:
            for email in sorted(failed_accounts):
                f.write(f"{email}\n")
                
        print(f"Added {email} to failed accounts list")
    except Exception as e:
        print(f"Error adding failed account: {e}")

def filter_out_failed_accounts(accounts: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    Filter out accounts that have previously failed to refresh.
    
    Args:
        accounts: List of account dictionaries with 'email' and 'password' keys
        
    Returns:
        Filtered list of accounts
    """
    failed_accounts = get_failed_accounts()
    
    if not failed_accounts:
        return accounts
        
    filtered_accounts = [
        account for account in accounts 
        if account.get('email') not in failed_accounts
    ]
    
    filtered_count = len(accounts) - len(filtered_accounts)
    if filtered_count > 0:
        print(f"Filtered out {filtered_count} previously failed accounts")
        
    return filtered_accounts

def clear_failed_accounts() -> None:
    """
    Clear the list of failed accounts.
    """
    try:
        if os.path.exists(FAILED_ACCOUNTS_FILE):
            os.remove(FAILED_ACCOUNTS_FILE)
            print("Cleared failed accounts list")
    except Exception as e:
        print(f"Error clearing failed accounts: {e}")

# For testing
if __name__ == "__main__":
    # Test adding a failed account
    add_failed_account("<EMAIL>")
    
    # Test getting failed accounts
    failed = get_failed_accounts()
    print(f"Failed accounts: {failed}")
    
    # Test filtering
    test_accounts = [
        {"email": "<EMAIL>", "password": "password1"},
        {"email": "<EMAIL>", "password": "password2"}
    ]
    filtered = filter_out_failed_accounts(test_accounts)
    print(f"Filtered accounts: {filtered}")
