import query_builder as qb
import asyncio
from tqdm import tqdm
from datetime import datetime, timedelta

steam_metrics = qb.Schema('metrics', db_name='steam')
steam_db = qb.db(db_name='steam')

ps_db = qb.db(db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

xbox_db = qb.db(db_name='xbox')
xbox_metrics = qb.Schema('metrics', db_name='xbox')

async def get_table_names(prefix) -> list[str]:
    tables_result = await qb.Query(xbox_db.pg_catalog.pg_tables) \
        .where(schemaname='metrics') \
        .where(qb.c_.tablename.op('~')(prefix+'_(24|25)')) \
        .fetchall()
    return [r['tablename'] for r in tables_result]

async def get_all_users(table_prefix):
    users = set()
    table_names = await get_table_names(table_prefix)
    for table_name in tqdm(table_names, desc=f"Processing {table_prefix} tables"):
        print(f"getting table {table_name}")
        try:
            table = getattr(xbox_metrics, table_name)
            account_id_result = await qb.Query(table).select('xuid').distinct().fetchall()
            users.update(r['xuid'] for r in account_id_result)
        except AttributeError:
            continue
    return users

async def find_inactive_users():
    active_users = await get_all_users('daily_active')
    await save_active_users(active_users)
    monitored_users = await get_all_users('daily_monitored')
    inactive_users = monitored_users - active_users
    await save_inactive_users(inactive_users)

async def save_inactive_users(inactive_users):
    with open('panel/accounts/xbox-inactive-users.tsv', 'w') as f:
        f.write("user_id\n")
        for user in inactive_users:
            f.write(f"{user}\n")
    print(f"Saved {len(inactive_users)} inactive users to panel/accounts/xbox-inactive-users.tsv")

async def save_active_users(active_users):
    with open('panel/accounts/xbox-active-users.tsv', 'w') as f:
        f.write("user_id\n")
        for user in active_users:
            f.write(f"{user}\n")
    print(f"Saved {len(active_users)} active users to panel/accounts/xbox-active-users.tsv")

async def get_users_for_date(date_str: str) -> set:
    users = set()
    table_name = f'daily_monitored_{date_str}'
    print(f"Getting table {table_name}")
    try:
        table = getattr(xbox_metrics, table_name)
        account_id_result = await qb.Query(table).select('xuid').distinct().fetchall()
        # Convert all user IDs to strings to ensure consistent type
        users.update(str(r['xuid']) for r in account_id_result)
    except AttributeError:
        print(f"Table {table_name} not found")
    return users

async def compare_daily_monitored_dates() -> int:
    first_day_2024 = "24_01_01"

    yesterday = (datetime.now() - timedelta(days=1)).strftime("%y_%m_%d")

    print(f"Comparing daily_monitored users between {first_day_2024} and {yesterday}")

    first_day_users = await get_users_for_date(first_day_2024)
    yesterday_users = await get_users_for_date(yesterday)

    differences = first_day_users.symmetric_difference(yesterday_users)
    diff_count = len(differences)

    print(f"First day users: {len(first_day_users)}")
    print(f"Yesterday users: {len(yesterday_users)}")
    print(f"Number of differences: {diff_count}")

    added_users = yesterday_users - first_day_users
    removed_users = first_day_users - yesterday_users

    print(f"Added users: {len(added_users)}")
    print(f"Removed users: {len(removed_users)}")

    return diff_count

async def list_available_tables():
    tables_result = await qb.Query(xbox_db.pg_catalog.pg_tables) \
        .where(schemaname='metrics') \
        .where(qb.c_.tablename.op('~')('daily_monitored_')) \
        .fetchall()

    table_names = [r['tablename'] for r in tables_result]
    print(f"Available daily_monitored tables: {len(table_names)}")
    for table in sorted(table_names):
        print(f"  - {table}")

    return table_names

async def find_inactive_users_from_file():
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%y_%m_%d")

    active_users = set()
    try:
        with open('panel/accounts/xbox-active-users.tsv', 'r') as f:
            first_line = f.readline().strip()
            if first_line != "user_id":
                active_users.add(str(first_line))
            for line in f:
                user_id = line.strip()
                if user_id:
                    active_users.add(str(user_id))
        print(f"Read {len(active_users)} active users from file")

        sample_active = list(active_users)[:5] if active_users else []
        print(f"Sample active users (first 5): {sample_active}")
        print(f"Type of first active user: {type(sample_active[0]) if sample_active else 'N/A'}")

    except FileNotFoundError:
        print("Active users file not found. Creating an empty set.")

    monitored_users = await get_users_for_date(yesterday)

    sample_monitored = list(monitored_users)[:5] if monitored_users else []
    print(f"Sample monitored users (first 5): {sample_monitored}")
    print(f"Type of first monitored user: {type(sample_monitored[0]) if sample_monitored else 'N/A'}")
    print(f"Found {len(monitored_users)} monitored users for {yesterday}")

    inactive_users = monitored_users - active_users
    print(f"Found {len(inactive_users)} inactive users")

    overlap = monitored_users.intersection(active_users)
    print(f"Overlap between monitored and active: {len(overlap)} users")

    with open('panel/accounts/xbox-inactive-users.tsv', 'w') as f:
        f.write("user_id\n")
        for user in inactive_users:
            f.write(f"{user}\n")
    print(f"Saved {len(inactive_users)} inactive users to panel/accounts/xbox-inactive-users.tsv")

    return inactive_users

if __name__ == "__main__":
    asyncio.run(find_inactive_users_from_file())
