import pandas as pd
from collections import Counter, defaultdict

df = pd.read_csv("steam_country_mappings.csv")

def common_strings(country_display_names: list):
    from collections import Counter
    import re

    def substrings(s, min_len=3):
        length = len(s)
        return {s[i:j] for i in range(length) for j in range(i + min_len, length + 1)}

    total = len(country_display_names)
    substring_counts = Counter()

    normalized = [s.strip().lower() for s in country_display_names]

    for s in normalized:
        seen = set()
        for substr in substrings(s):
            if substr not in seen:
                substring_counts[substr] += 1
                seen.add(substr)

    candidates = [
        (substr, count) for substr, count in substring_counts.items()
        if count / total >= 0.5
    ]

    if not candidates:
        print("No common string found for: ", country_display_names)
        return None

    candidates.sort(key=lambda x: (-len(x[0]), -x[1]))
    return candidates[0][0].strip().title()

def main():
    country_mapping = defaultdict(list)
    for _, row in df.iterrows():
        country_mapping[row['country_name']].append(row['country_code'])

    final_mapping = {}
    for country_name, country_codes in country_mapping.items():
        most_common = common_strings(country_codes)
        if most_common:
            final_mapping[country_name] = most_common

    output_df = pd.DataFrame([
        {"country_name": name, "country_code": code}
        for name, code in final_mapping.items()
    ])
    output_df.to_csv("cleaned_country_mapping.csv", index=False)

    print("Saved cleaned mapping to cleaned_country_mapping.csv")

if __name__ == "__main__":
    main()
