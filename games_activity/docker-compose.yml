services:
  db:
    image: postgres:16.4
    container_name: steam_test_db
    restart: always
    volumes:
      - db_volume:/var/lib/postgresql/data
    ports:
      - "7655:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
  
  steam-library:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    command: python -m library.steam_library.get_library

  cookie-refresh:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    command: python -m library.steam_library.cookie_refresh
    
  full-steam-run:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    command: python -u -m library.steam_library.full_steam_process
    network_mode: "container:wireguard"

  wireguard:
    image: lscr.io/linuxserver/wireguard:latest
    container_name: wireguard
    cap_add:
      - NET_ADMIN
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC
    volumes:
      - ./config:/config/wg_confs
    sysctls:
      - net.ipv4.conf.all.src_valid_mark=1
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - default

volumes:
  db_volume:

networks:
  default:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1400